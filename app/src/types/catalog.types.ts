/**
 * Catalog interfaces and related types for the product item catalog system
 * These interfaces match the backend DTOs exactly for proper API integration
 */

/**
 * Represents a product category in the system - matches ProductCategoryResponseDTO
 */
export interface Category {
  id?: number; // Optional for create requests, required for responses
  name: string;
  description: string;
  imagePath?: string | null;
  imageUrl?: string | null; // Presigned URL for direct image viewing
}

/**
 * Represents a product subcategory in the system - matches ProductSubCategoryDTO
 */
export interface Subcategory {
  id?: number; // Optional for create requests, required for responses
  name: string;
  description: string;
  categoryId: number;
  imagePath?: string | null;
  imageUrl?: string | null; // Presigned URL for direct image viewing

  // For UI display (not from backend DTO)
  categoryName?: string;
}

/**
 * Represents a product in the system - matches ProductDTO
 */
export interface Product {
  id?: number; // Optional for create requests, required for responses
  name: string;
  description: string;
  
  categoryId: number;
  subCategoryId: number;
  imageUrl?: string | null;

  // For UI display (not from backend DTO)
  subcategoryName?: string;
  categoryName?: string;
}

/**
 * Request object for creating a new category - matches ProductCategoryDTO structure
 */
export interface CreateCategoryRequest {
  name: string;
  description: string;
  imageFile?: File | null;
}

/**
 * Request object for updating an existing category - matches ProductCategoryDTO structure
 */
export interface UpdateCategoryRequest {
  name: string;
  description: string;
  imageFile?: File | null;
  deleteImage?: boolean;
}

/**
 * Request object for creating a new subcategory - matches ProductSubCategoryDTO structure
 */
export interface CreateSubcategoryRequest {
  name: string;
  description: string;
  categoryId: number;
  imageFile?: File | null;
}

/**
 * Request object for updating an existing subcategory - matches ProductSubCategoryDTO structure
 */
export interface UpdateSubcategoryRequest {
  name: string;
  description: string;
  categoryId: number;
  imageFile?: File | null;
  deleteImage?: boolean;
}

/**
 * Request object for creating a new product - matches ProductRequestDTO structure
 */
export interface CreateProductRequest {
  name: string;
  description: string;
  
  categoryId: number;
  subCategoryId: number;
  imageFile?: File | null;
  attributes?: string;
}

/**
 * Request object for updating an existing product - matches ProductRequestDTO structure
 */
export interface UpdateProductRequest {
  name: string;
  description: string;
  
  categoryId: number;
  subCategoryId: number;
  imageFile?: File | null;
  deleteImage?: boolean;
  attributes?: string;
}

/**
 * Represents an organization product in the system - matches OrganizationProductResponseDTO
 */
export interface OrganizationProduct {
  id?: number;
  name: string;
  description: string;
  organizationId: number;
  categoryId: number;
  subCategoryId: number;
  imageUrl?: string | null;
  attributes?: string;
  // For UI display (not from backend DTO)
  subcategoryName?: string;
  categoryName?: string;
}

/**
 * Request object for creating a new organization product - matches OrganizationProductRequestDTO structure
 */
export interface CreateOrganizationProductRequest {
  name: string;
  description: string;
  organizationId: number;
  categoryId: number;
  subCategoryId: number;
  imageFile?: File | null;
  attributes?: string;
}

/**
 * Request object for updating an existing organization product - matches OrganizationProductRequestDTO structure
 */
export interface UpdateOrganizationProductRequest {
  name: string;
  description: string;
  organizationId: number;
  categoryId: number;
  subCategoryId: number;
  imageFile?: File | null;
  deleteImage?: boolean;
  attributes?: string;
}

/**
 * Purchase Request related types
 */

/**
 * Enum for procurement source - matches backend ProcurementSource enum
 */
export enum ProcurementSource {
  RFQ = 'RFQ',
  ONLINE = 'ONLINE',
  DIRECT_VENDOR = 'DIRECT_VENDOR',
  DIRECT_SHOP = 'DIRECT_SHOP'
}

/**
 * Enum for request type - matches backend RequestType enum
 */
export enum RequestType {
  BUY = 'BUY',
  BUY_OLD = 'BUY_OLD',
  RENT = 'RENT'
}

/**
 * Enum for request status - matches backend RequestStatus enum
 */
export enum RequestStatus {
  PENDING = 'PENDING',
  APPROVED = 'APPROVED',
  REJECTED = 'REJECTED',
  CANCELLED = 'CANCELLED'
}

/**
 * Represents a purchase request item - matches PurchaseRequestItemDTO
 */
export interface PurchaseRequestItem {
  id?: number;
  purchaseRequestId?: number;
  productId?: number;
  organizationProductId?: number;
  quantity: number;
  onlineUrl?: string;
}

/**
 * Represents a purchase request - matches PurchaseRequestDTO
 */
export interface PurchaseRequest {
  id?: number;
  requestedById: number;
  organizationId: number;
  requestStatus?: RequestStatus;
  requestType: RequestType;
  procurementSource: ProcurementSource;
  description?: string;
  requestDate?: string; // ISO string format
  expectedDeliveryDate: string; // ISO string format
  productCategoryId: number;
  purchaseRequestItems: PurchaseRequestItem[];
  currentApproverId?: number; // ID of the user currently approving this request
  lastActionUserId?: number; // ID of the user who last acted on this request
}

/**
 * Request object for creating a new purchase request
 */
export interface CreatePurchaseRequestRequest {
  requestedById: number;
  organizationId: number;
  requestType: RequestType;
  procurementSource: ProcurementSource;
  description?: string;
  expectedDeliveryDate: string; // ISO string format
  productCategoryId: number;
  purchaseRequestItems: PurchaseRequestItem[];
}

/**
 * Product selection item for the form (UI-specific)
 */
export interface ProductSelectionItem {
  id: string; // Unique identifier for the form item
  type: 'product' | 'organizationProduct';
  productId?: number;
  organizationProductId?: number;
  productName: string;
  quantity: number;
  onlineUrl?: string;
}