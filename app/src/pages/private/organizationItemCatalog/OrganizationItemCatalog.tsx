import Button from '@/components/ui/Button/Button';
import { ColumnConfig, DataGrid } from '@/components/ui/DataGrid/DataGrid';
import Modal from '@/components/ui/Modal/Modal';
import Toast, { ToastRef } from '@/components/ui/Toast/Toast';
import Typography from '@/components/ui/Typography';
import { useOrganizationContext } from '@/context/OrganizationContext';
import { useCategories, useDeleteOrganizationProduct, useOrganizationProducts, useProducts, useSubcategories } from '@/hooks/useCatalog';
import { addProductRoute } from '@/routes/private/addProduct.route';
import { editProductRoute } from '@/routes/private/editProduct.route';
import { useNavigate } from '@tanstack/react-router';
import { Dropdown } from 'primereact/dropdown';
import React, { useEffect, useMemo, useRef, useState } from 'react';
import './OrganizationItemCatalog.css';
import ImageModal from '@/components/approvalWorkflow/ImageModal';

const OrganizationCatalog: React.FC = () => {
  const toast = useRef<ToastRef>(null);
  const { organizationId } = useOrganizationContext();
  const navigate = useNavigate();
  if (!organizationId) {
    return <div>Organization ID is required.</div>;
  }

  // State for modals
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [organizationProductToDelete, setOrganizationProductToDelete] = useState<any>(null);

  const [isImageModalOpen, setIsImageModalOpen] = useState(false);
  const [selectedImageUrl, setSelectedImageUrl] = useState<string | null>(null);

  // Filter state
  const [selectedCategory, setSelectedCategory] = useState<number | null>(null);
  const [selectedSubcategory, setSelectedSubcategory] = useState<number | null>(null);

  // Fetch products
  const { data: productsData, isLoading: isProductsLoading } = useProducts({ page: 1, limit: 20, sort: 'name,asc' });
  const products = productsData?.data || [];
  // const productsTotal = productsData?.total || 0;

  // Fetch organization products
  const { data: organizationProductsData, isLoading: isOrganizationProductsLoading } = useOrganizationProducts(organizationId, { page: 1, limit: 20, sort: 'name,asc' });
  const organizationProducts = organizationProductsData?.data || [];

  // Fetch categories and subcategories
  const { data: categoriesData } = useCategories();
  const categories = categoriesData?.data || [];
  const categoryOptions = categories.map((cat: any) => ({ label: cat.name, value: cat.id }));

  const { data: subcategoriesData } = useSubcategories();
  const subcategories = subcategoriesData?.data || [];
  const subcategoryOptions = subcategories.map((sub: any) => ({ label: sub.name, value: sub.id }));

  // Mutations
  const deleteOrganizationProduct = useDeleteOrganizationProduct()

  // Handle delete click
  const handleDeleteClick = (product: any) => {
    setOrganizationProductToDelete(product);
    setIsDeleteModalOpen(true);
  };

  // Handle image modal open
  const handleImageModalOpen = (imageUrl: string) => {
    setSelectedImageUrl(imageUrl);
    setIsImageModalOpen(true);
  };

  // Handle delete confirmation
  const handleDeleteConfirm = async () => {
    if (organizationProductToDelete) {
      try {
        await deleteOrganizationProduct.mutateAsync({ organizationId: organizationProductToDelete.organizationId, id: organizationProductToDelete.id });
        toast.current?.showSuccess('Product deleted successfully');
        setIsDeleteModalOpen(false);
        setOrganizationProductToDelete(null);
      } catch (error) {
        toast.current?.showError('Failed to delete product');
      }
    }
  };

  const handleEdit = (organizationProduct: any) => {
    navigate({
      to: editProductRoute.to,
      search: { id: organizationProduct.id.toString() }
    });
  };

  // Table columns
  const columns: ColumnConfig[] = [
    { field: 'name', header: 'Product Name', sortable: true },
    { field: 'categoryName', header: 'Category', sortable: true },
    { field: 'subcategoryName', header: 'Subcategory', sortable: true },
    { field: 'description', header: 'Description' },
    {
      field: 'actions',
      header: 'Actions',
      sortable: false,
      body: (rowData) =>
        <>
          <div className="flex gap-2">
            {rowData.isOrganizationProduct && (
              <>
                <Button
                  icon="pi pi-pencil"
                  variant="outline"
                  size="small"
                  onClick={() => handleEdit(rowData)}
                  aria-label="Edit"
                />
                <Button
                  icon="pi pi-trash"
                  variant="outline"
                  size="small"
                  onClick={() => handleDeleteClick(rowData)}
                  aria-label="Delete"
                  className="p-button-danger"
                />
              </>
            )}
            <Button
              visible={!!rowData.imageUrl}
              icon="pi pi-eye"
              variant="outline"
              size="small"
              onClick={() => handleImageModalOpen(rowData.imageUrl)}
              aria-label="Image"
            />
          </div>
        </>
    }
  ];


  useEffect(() => {
    if (typeof selectedCategory !== "number") {
      setSelectedCategory(null);
    }
  }, [selectedCategory]);

  useEffect(() => {
    if (typeof selectedSubcategory !== "number") {
      setSelectedSubcategory(null);
    }
  }, [selectedSubcategory]);

  // Filter products in-memory
  const filteredAllProducts = useMemo(() => {
      const orgProductsWithFlag = organizationProducts.map((item: any) => ({ ...item, isOrganizationProduct: true }));
    const all = [...products, ...orgProductsWithFlag];
    return all.filter((item: any) => {
      const matchCategory = selectedCategory ? item.categoryId === selectedCategory : true;
      const matchSubcategory = selectedSubcategory ? item.subCategoryId === selectedSubcategory : true;
      return matchCategory && matchSubcategory;
    });
  }, [products, organizationProducts, selectedCategory, selectedSubcategory]);
  // Handlers
  return (
    <div className="organization-item-catalog p-4">
      <Toast ref={toast} position="top-right" />
      <Typography variant="h5" weight="semibold" className="mb-4">
        Organization Catalogue
      </Typography>
      <div className='flex justify-content-between gap-2'>
        <div className="filter-container mb-4 flex gap-2">
          <Dropdown
            value={selectedCategory}
            options={[{ label: 'All Categories', value: null }, ...categoryOptions]}
            onChange={e => {
              setSelectedCategory(e.value);
              setSelectedSubcategory(null); // Reset subcategory filter when category changes
            }}
            placeholder="Filter by Category"
            className="category-filter"
            showClear
          />
          <Dropdown
            value={selectedSubcategory}
            options={[
              { label: 'All Subcategories', value: null },
              ...(
                selectedCategory
                  ? subcategories.filter((sub: any) => sub.categoryId === selectedCategory).map((sub: any) => ({ label: sub.name, value: sub.id }))
                  : subcategoryOptions
              )
            ]}
            onChange={e => setSelectedSubcategory(e.value)}
            placeholder="Filter by Subcategory"
            className="subcategory-filter"
            showClear
            disabled={!selectedCategory && subcategoryOptions.length === 0}
          />
        </div>
        <div className="flex gap-2 mb-4">
          <Button variant="primary" size="small" onClick={() => navigate({ to: addProductRoute.to })}>
            Add Product
          </Button>
        </div>
      </div>
      <DataGrid
        value={filteredAllProducts}
        columns={columns}
        totalRecords={filteredAllProducts.length}
        loading={isProductsLoading && isOrganizationProductsLoading}
        rows={20}
        rowsPerPageOptions={[20, 50, 100]}
        showGridLines={true}
        stripedRows={true}
        emptyMessage="No products found"
      />
      {/* Image Modal */}
      <ImageModal
        imageUrl={selectedImageUrl || ''}
        isOpen={isImageModalOpen}
        onClose={() => {
          setIsImageModalOpen(false);
          setSelectedImageUrl(null);
        }}
      />
      {/* Delete Confirmation Modal */}
      <Modal
        visible={isDeleteModalOpen}
        onHide={() => {
          setIsDeleteModalOpen(false);
          setOrganizationProductToDelete(null);
        }}
        header="Confirm Delete"
        footerButtons={[
          {
            label: 'Cancel',
            icon: 'pi pi-times',
            variant: 'outline',
            onClick: () => {
              setIsDeleteModalOpen(false);
              setOrganizationProductToDelete(null);
            },
          },
          {
            label: 'Delete',
            icon: 'pi pi-trash',
            variant: 'danger',
            onClick: handleDeleteConfirm,
          },
        ]}
      >
        <p>Are you sure you want to delete this product?</p>
        {organizationProductToDelete && (
          <div className="mt-3">
            <p><strong>Name:</strong> {organizationProductToDelete.name}</p>
            <p><strong>Category:</strong> {organizationProductToDelete.categoryName}</p>
            <p><strong>Subcategory:</strong> {organizationProductToDelete.subcategoryName}</p>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default OrganizationCatalog;