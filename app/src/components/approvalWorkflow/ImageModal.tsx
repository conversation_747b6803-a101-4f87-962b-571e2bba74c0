import Modal from '@components/ui/Modal/Modal';
import React from 'react';

interface ImageModalProps {
    imageUrl: string;
    isOpen: boolean;
    onClose: () => void;
}

const ImageModal: React.FC<ImageModalProps> = ({ imageUrl, isOpen, onClose }) => {
    return (
        <Modal
            visible={isOpen}
            onHide={onClose}
            header="Product Image"
            footerButtons={[
                {
                    label: 'Close',
                    onClick: onClose,
                },
            ]}
        >
            <div style={{ textAlign: 'center' }}>
                <img
                    src={imageUrl}
                    alt="Product"
                    style={{ maxWidth: '100%', maxHeight: '60vh', borderRadius: 8 }}
                />
            </div>
        </Modal>
    );
};

export default ImageModal;
