import React, { useState, useEffect } from 'react';
import { Dropdown } from 'primereact/dropdown';
import { But<PERSON> } from 'primereact/button';
import { ProgressSpinner } from 'primereact/progressspinner';
import { MultiSelect } from 'primereact/multiselect'; // Import MultiSelect
import { WorkflowCriteria, CriteriaFieldType } from '@/types/approvalWorkflow.types';
import {
  fieldTypeOptions,
  operatorOptions,
  logicConnectorOptions
} from '@/data/mockApprovalData';
import { DepartmentService } from '@/services/api/departmentService';
import { DesignationService } from '@/services/api/designationService';
import { LocationService } from '@/services/api/locationService';
import { UserService } from '@/services/api/userService';
import './CriteriaBuilder.css';
import { useOrganizationContext } from '@/context/OrganizationContext';

interface CriteriaBuilderProps {
  criteria: WorkflowCriteria[];
  onChange: (criteria: WorkflowCriteria[]) => void;
  error?: string;
}

interface DropdownOption {
  label: string;
  value: number; // Values should be numbers to match `WorkflowCriteria`
}

const CriteriaBuilder: React.FC<CriteriaBuilderProps> = ({
  criteria,
  onChange,
  error
}) => {
  const { organizationId } = useOrganizationContext();

  // Helper function to create deep copies of criteria to prevent reference sharing
  const deepCopyCriteria = (criteriaArray: WorkflowCriteria[]): WorkflowCriteria[] => {
    return criteriaArray.map(c => ({
      id: c.id,
      fieldType: c.fieldType,
      operator: c.operator,
      values: Array.isArray(c.values) ? [...c.values] : [],
      logicConnector: c.logicConnector
    }));
  };

  // State for dropdown options
  const [departments, setDepartments] = useState<DropdownOption[]>([]);
  const [designations, setDesignations] = useState<DropdownOption[]>([]);
  const [locations, setLocations] = useState<DropdownOption[]>([]);
  const [employees, setEmployees] = useState<DropdownOption[]>([]);

  // Loading states
  const [loadingStates, setLoadingStates] = useState({
    departments: false,
    designations: false,
    locations: false,
    employees: false
  });

  // Error states
  const [errorStates, setErrorStates] = useState({
    departments: '',
    designations: '',
    locations: '',
    employees: ''
  });

  // Fetch data on component mount
  useEffect(() => {
    fetchAllData();
  }, []);

  const fetchAllData = async () => {
    await Promise.all([
      fetchDepartments(),
      fetchDesignations(),
      fetchLocations(),
      fetchEmployees()
    ]);
  };

  const fetchDepartments = async () => {
    setLoadingStates(prev => ({ ...prev, departments: true }));
    setErrorStates(prev => ({ ...prev, departments: '' }));

    try {
      if (!organizationId || organizationId <= 0) {
        throw new Error('Organization ID is not defined');
      }
      const departmentData = await DepartmentService.getDepartments(organizationId);
      const options = departmentData
        .filter(dept => dept.id != null)
        .map(dept => ({
          label: dept.name,
          value: dept.id!
        }));
      setDepartments(options);
    } catch (error) {
      console.error('Error fetching departments:', error);
      setErrorStates(prev => ({ ...prev, departments: 'Failed to load departments' }));
    } finally {
      setLoadingStates(prev => ({ ...prev, departments: false }));
    }
  };

  const fetchDesignations = async () => {
    setLoadingStates(prev => ({ ...prev, designations: true }));
    setErrorStates(prev => ({ ...prev, designations: '' }));

    try {
      const designationData = await DesignationService.getDesignations(organizationId as number);
      const options = designationData.data
        .map(designation => ({
          label: designation.name,
          value: parseInt(designation.id, 10)
        }))
        .filter(option => !isNaN(option.value));
      setDesignations(options);
    } catch (error) {
      console.error('Error fetching designations:', error);
      setErrorStates(prev => ({ ...prev, designations: 'Failed to load designations' }));
    } finally {
      setLoadingStates(prev => ({ ...prev, designations: false }));
    }
  };

  const fetchLocations = async () => {
    setLoadingStates(prev => ({ ...prev, locations: true }));
    setErrorStates(prev => ({ ...prev, locations: '' }));

    try {
      const locationData = await LocationService.getAllLocations(organizationId);
      const options = locationData
        .filter(loc => loc.id != null)
        .map(location => ({
          label: location.name,
          value: location.id!
        }));
      setLocations(options);
    } catch (error) {
      console.error('Error fetching locations:', error);
      setErrorStates(prev => ({ ...prev, locations: 'Failed to load locations' }));
    } finally {
      setLoadingStates(prev => ({ ...prev, locations: false }));
    }
  };

  const fetchEmployees = async () => {
    setLoadingStates(prev => ({ ...prev, employees: true }));
    setErrorStates(prev => ({ ...prev, employees: '' }));

    try {
      if (!organizationId || organizationId <= 0) {
        throw new Error('Organization ID is not defined');
      }
      const userData = await UserService.getUsers(organizationId);
      const options = userData
        .filter(user => user.id != null)
        .map(user => ({
          label: user.name,
          value: user.id!
        }));
      setEmployees(options);
    } catch (error) {
      console.error('Error fetching employees:', error);
      setErrorStates(prev => ({ ...prev, employees: 'Failed to load employees' }));
    } finally {
      setLoadingStates(prev => ({ ...prev, employees: false }));
    }
  };

  // Add new criteria row
  const addCriteria = () => {
    const newCriteria: WorkflowCriteria = {
      id: `criteria-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`, // More unique ID
      fieldType: 'department',
      operator: 'IS',
      values: [], // Use `values` array for multi-select
      logicConnector: criteria.length > 0 ? 'AND' : undefined
    };

    // Create deep copies of existing criteria to prevent reference sharing
    const existingCriteria = deepCopyCriteria(criteria);

    onChange([...existingCriteria, newCriteria]);
  };

  // Remove criteria row
  const removeCriteria = (id: string) => {
    const filteredCriteria = criteria.filter(c => c.id !== id);

    // Create deep copies of remaining criteria to prevent reference sharing
    const updatedCriteria = deepCopyCriteria(filteredCriteria);

    // Remove logic connector from the last item if it exists
    if (updatedCriteria.length > 0) {
      updatedCriteria[updatedCriteria.length - 1].logicConnector = undefined;
    }

    onChange(updatedCriteria);
  };

  // Update criteria field
  const updateCriteria = (id: string, field: keyof WorkflowCriteria, value: any) => {
    const updatedCriteria = criteria.map(c => {
      if (c.id === id) {
        // Create a deep copy of the criteria object to prevent reference sharing
        const updated: WorkflowCriteria = {
          id: c.id,
          fieldType: c.fieldType,
          operator: c.operator,
          values: Array.isArray(c.values) ? [...c.values] : [], // Deep copy the values array
          logicConnector: c.logicConnector
        };

        // Update the specific field with proper type handling
        if (field === 'values') {
          // Ensure values is always a new array to prevent reference sharing
          updated.values = Array.isArray(value) ? [...value] : [];
        } else if (field === 'fieldType') {
          updated.fieldType = value;
          // Reset values when field type changes
          updated.values = [];
        } else if (field === 'operator') {
          updated.operator = value;
        } else if (field === 'logicConnector') {
          updated.logicConnector = value;
        } else if (field === 'id') {
          updated.id = value;
        }

        return updated;
      }
      // Return a deep copy of unchanged criteria to prevent reference sharing
      return deepCopyCriteria([c])[0];
    });

    onChange(updatedCriteria);
  };

  // Get options for value dropdown based on field type
  const getValueOptions = (fieldType: CriteriaFieldType) => {
    switch (fieldType) {
      case 'department':
        return departments;
      case 'designation':
        return designations;
      case 'location':
        return locations;
      case 'employee':
        return employees;
      default:
        return [];
    }
  };

  // Get loading state for field type
  const getLoadingState = (fieldType: CriteriaFieldType) => {
    switch (fieldType) {
      case 'department':
        return loadingStates.departments;
      case 'designation':
        return loadingStates.designations;
      case 'location':
        return loadingStates.locations;
      case 'employee':
        return loadingStates.employees;
      default:
        return false;
    }
  };

  // Get error state for field type
  const getErrorState = (fieldType: CriteriaFieldType) => {
    switch (fieldType) {
      case 'department':
        return errorStates.departments;
      case 'designation':
        return errorStates.designations;
      case 'location':
        return errorStates.locations;
      case 'employee':
        return errorStates.employees;
      default:
        return '';
    }
  };

  // Get display text for selected values
  const getValuesDisplayText = (fieldType: CriteriaFieldType, values: number[]) => {
    if (!values || values.length === 0) {
      return '[Select Value(s)]';
    }
    const options = getValueOptions(fieldType);
    const selectedLabels = values.map(value => {
      const option = options.find(opt => opt.value === value);
      return option ? `"${option.label}"` : `"${value}"`;
    });
    return selectedLabels.join(' OR ');
  };

  return (
    <div className="criteria-builder">
      {criteria.length === 0 && (
        <div className="empty-criteria">
          <p className="text-center text-600 mb-3">
            No criteria defined. Click "Add Criteria" to get started.
          </p>
        </div>
      )}

      {criteria.map((criterion, index) => (
        <div key={criterion.id} className="criteria-row">
          <div className="criteria-fields">
            {/* Field Type Dropdown */}
            <div className="criteria-field">
              <label className="field-label">Field</label>
              <Dropdown
                value={criterion.fieldType}
                options={fieldTypeOptions}
                onChange={(e) => updateCriteria(criterion.id, 'fieldType', e.value)}
                placeholder="Select field"
                className="w-full"
              />
            </div>

            {/* Operator Dropdown */}
            <div className="criteria-field">
              <label className="field-label">Operator</label>
              <Dropdown
                value={criterion.operator}
                options={operatorOptions}
                onChange={(e) => updateCriteria(criterion.id, 'operator', e.value)}
                placeholder="Select operator"
                className="w-full"
              />
            </div>

            {/* Value MultiSelect */}
            <div className="criteria-field">
              <label className="field-label">Value</label>
              <div className="value-dropdown-container">
                {getLoadingState(criterion.fieldType) ? (
                  <div className="loading-container">
                    <ProgressSpinner style={{ width: '20px', height: '20px' }} strokeWidth="4" />
                    <span className="loading-text">Loading...</span>
                  </div>
                ) : (
                  <MultiSelect
                    value={criterion.values}
                    options={getValueOptions(criterion.fieldType)}
                    onChange={(e) => updateCriteria(criterion.id, 'values', e.value)}
                    placeholder={getErrorState(criterion.fieldType) || "Select value(s)"}
                    className={`w-full ${getErrorState(criterion.fieldType) ? 'p-invalid' : ''}`}
                    filter
                    display="chip"
                    emptyMessage={getErrorState(criterion.fieldType) || "No options available"}
                    disabled={!!getErrorState(criterion.fieldType)}
                    maxSelectedLabels={3}
                    optionLabel="label"
                    optionValue="value"
                  />
                )}
                {getErrorState(criterion.fieldType) && (
                  <small className="p-error">{getErrorState(criterion.fieldType)}</small>
                )}
              </div>
            </div>

            {/* Logic Connector (for all except last) */}
            {index < criteria.length - 1 && (
              <div className="criteria-field">
                <label className="field-label">Logic</label>
                <Dropdown
                  value={criterion.logicConnector}
                  options={logicConnectorOptions}
                  onChange={(e) => updateCriteria(criterion.id, 'logicConnector', e.value)}
                  placeholder="AND/OR"
                  className="w-full"
                />
              </div>
            )}

            {/* Remove Button */}
            <div className="criteria-actions">
              <Button
                type="button"
                icon="pi pi-trash"
                className="p-button-danger p-button-text"
                onClick={() => removeCriteria(criterion.id)}
                tooltip="Remove criteria"
                tooltipOptions={{ position: 'top' }}
              />
            </div>
          </div>
        </div>
      ))}

      {/* Add Criteria Button */}
      <div className="add-criteria-section">
        <Button
          type="button"
          icon="pi pi-plus"
          label="Add Criteria"
          className="p-button-outlined"
          onClick={addCriteria}
        />
      </div>

      {/* Error Message */}
      {error && (
        <small className="p-error criteria-error">{error}</small>
      )}

      {/* Criteria Summary */}
      {criteria.length > 0 && (
        <div className="criteria-summary">
          <h4 className="summary-title">Criteria Summary:</h4>
          <div className="summary-text">
            The workflow will be triggered when{' '}
            {criteria.map((criterion, index) => (
              <span key={criterion.id}>
                <strong>{fieldTypeOptions.find(f => f.value === criterion.fieldType)?.label}</strong>
                {' '}
                <em>{criterion.operator.replace('_', ' ').toLowerCase()}</em>
                {' '}
                <strong>{getValuesDisplayText(criterion.fieldType, criterion.values)}</strong>
                {criterion.logicConnector && index < criteria.length - 1 && (
                  <>
                    {' '}
                    <span className="logic-word">{criterion.logicConnector.toLowerCase()}</span>
                    {' '}
                  </>
                )}
              </span>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default CriteriaBuilder;