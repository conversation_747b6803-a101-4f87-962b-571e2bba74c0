import { Button as PrimeButton } from 'primereact/button';
import { ButtonHTMLAttributes } from 'react';
import 'primereact/resources/themes/lara-light-indigo/theme.css';
import 'primereact/resources/primereact.min.css';
import './Button.css';

export type ButtonVariant = 'primary' | 'secondary' | 'outline' | 'danger';
export type ButtonSize = 'small' | 'medium' | 'large';

interface ButtonProps extends ButtonHTMLAttributes<HTMLButtonElement> {
    variant?: ButtonVariant;
    size?: ButtonSize;
    isLoading?: boolean;
    leftIcon?: React.ReactNode;
    rightIcon?: React.ReactNode;
    fullWidth?: boolean;
    icon?: string;
    text?: boolean;
    visible?: boolean;
}

const Button = ({
    variant = 'primary',
    size = 'medium',
    isLoading = false,
    leftIcon,
    rightIcon,
    fullWidth = false,
    className = '',
    text = false,
    children,
    ...props
}: ButtonProps) => {
    const getVariantClass = () => {
        switch (variant) {
            case 'primary':
                return 'button--primary';
            case 'secondary':
                return 'button--secondary';
            case 'danger':
                return 'button--danger';
            case 'outline':
                return 'button--outline';
            default:
                return '';
        }
    };

    const getSizeClass = () => {
        switch (size) {
            case 'small':
                return 'button--small';
            case 'medium':
                return 'button--medium';
            case 'large':
                return 'button--large';
            default:
                return '';
        }
    };

    const baseClass = 'button';
    const variantClass = getVariantClass();
    const sizeClass = getSizeClass();
    const widthClass = fullWidth ? 'button--full-width' : '';
    const loadingClass = isLoading ? 'button--loading' : '';

    const buttonClasses = `${baseClass} ${variantClass} ${sizeClass} ${widthClass} ${loadingClass} ${className}`;

    return (
        <PrimeButton
            className={buttonClasses}
            disabled={isLoading || props.disabled}
            text
            {...props}
        >
            {isLoading ? (
                <i className="pi pi-spin pi-spinner button__spinner" />
            ) : (
                <>
                    {leftIcon}
                    {children}
                    {rightIcon}
                </>
            )}
        </PrimeButton>
    );
};

export default Button; 