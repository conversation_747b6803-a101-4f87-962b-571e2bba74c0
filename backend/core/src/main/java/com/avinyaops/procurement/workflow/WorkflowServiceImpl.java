package com.avinyaops.procurement.workflow;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.avinyaops.procurement.exception.AvinyaException;
import com.avinyaops.procurement.organization.Organization;
import com.avinyaops.procurement.organization.OrganizationService;
import com.avinyaops.procurement.organization.department.Department;
import com.avinyaops.procurement.organization.department.DepartmentRepository;
import com.avinyaops.procurement.organization.designation.Designation;
import com.avinyaops.procurement.organization.location.Location;
import com.avinyaops.procurement.product.ResourceNotFoundException;
import com.avinyaops.procurement.user.Role;
import com.avinyaops.procurement.user.User;
import com.avinyaops.procurement.user.UserRepository;
import com.avinyaops.procurement.user.UserService;
import com.avinyaops.procurement.workflow.purchaserequest.PurchaseRequest;
import com.avinyaops.procurement.workflow.purchaserequest.PurchaseRequestRepository;
import com.avinyaops.procurement.workflow.purchaserequest.RequestStatus;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Service
@AllArgsConstructor
@Transactional
@Slf4j
public class WorkflowServiceImpl implements WorkflowService {

    private final WorkflowDefinitionRepository workflowDefinitionRepository;
    private final RecordApprovalRepository recordApprovalRepository;
    private final ApproverTimelineRepository approverTimelineRepository;
    private final OrganizationFormsRepository organizationFormsRepository;
    private final UserRepository userRepository;
    private final DepartmentRepository departmentRepository;
    private final UserService userService;
    private final OrganizationService organizationService;
    private final PurchaseRequestRepository purchaseRequestRepository;
    private final ExpressionParser expressionParser = new SpelExpressionParser();
    
    @Override
    @Transactional
    public RecordApprovalResponse initiateWorkflow(Long organizationId, Long formId, Long recordId, Long initiatorUserId) {
        if (recordApprovalRepository.findOneByOrganization_IdAndOrganizationForms_IdAndRecordId(organizationId, formId, recordId).isPresent()) {
            throw new RuntimeException("approval record already in progeress for this record id: "+ recordId);
        }

        Organization organization = organizationService.getOrganizationEntity(organizationId);
        
        Optional<OrganizationForms> formOptional = organizationFormsRepository.findByOrganizationIdAndId(organizationId, formId);
        if (!formOptional.isPresent()) {
            throw new RuntimeException("Form not found");
        }
        
        // TODO: get this from security utils
        User initiatorUser = userService.getById(initiatorUserId);
        Department initiatorUserDepartment = initiatorUser.getDepartment();
        Designation initiatorUserDesignation = initiatorUser.getDesignation();
        Location initiatorUserLocation = initiatorUser.getLocation();
        
        List<WorkflowDefinition> workflowDefinitions = workflowDefinitionRepository.findAllByOrganizationIdAndFormId(organizationId, formId);
        if (workflowDefinitions.isEmpty()) {
            throw new RuntimeException("No workflow definition found for this form");
        }

        WorkflowDefinition selectedWorkflowDefinition = null;
        for(WorkflowDefinition workflowDefinition: workflowDefinitions){
            CriteriaConfig criteriaConfig = workflowDefinition.getCriteriaConfig();
            List<WorkflowCriteria> workflowCriteriaList = criteriaConfig.getWorkflowCriteriaList();
            StringBuilder resultPattern = new StringBuilder();
            log.info("this is the workflow definition name: "+ workflowDefinition.getName());
            for(int i=0;i<workflowCriteriaList.size();i++){
                WorkflowCriteria criteria = workflowCriteriaList.get(i);
                log.info(criteria.toString());
                switch (criteria.getField()) {
                    case "department": {
                        log.info("department field");
                        Long departmentId = Optional.ofNullable(initiatorUserDepartment).map(Department::getId).orElse(0L);
                        if(criteria.getOperation() == CriteriaOperations.IS){
                            resultPattern.append(criteria.getRuleValues().stream().anyMatch(
                                    val -> val.equals(departmentId)));
                        } else if(criteria.getOperation() == CriteriaOperations.IS_NOT){
                            resultPattern.append(criteria.getRuleValues().stream().noneMatch(
                                    val -> val.equals(departmentId)));
                        } else {
                            throw new IllegalArgumentException("Unexpected operation value" + criteria.getOperation());
                        }
                        break;
                    }
                    case "designation": {
                        log.info("designation field");
                        Long designationId = Optional.ofNullable(initiatorUserDesignation).map(Designation::getId).orElse(0L);
                        if(criteria.getOperation() == CriteriaOperations.IS){
                            resultPattern.append(criteria.getRuleValues().stream().anyMatch(
                                    val -> val.equals(designationId)));
                        } else if(criteria.getOperation() == CriteriaOperations.IS_NOT){
                            resultPattern.append(criteria.getRuleValues().stream().noneMatch(
                                    val -> val.equals(designationId)));
                        } else {
                            throw new IllegalArgumentException("Unexpected operation value" + criteria.getOperation());
                        }
                        break;
                    }
                    case "location": {
                        log.info("location field");
                        Long locationId = Optional.ofNullable(initiatorUserLocation).map(Location::getId).orElse(0L);
                        if(criteria.getOperation() == CriteriaOperations.IS){
                            resultPattern.append(criteria.getRuleValues().stream().anyMatch(
                                    val -> val.equals(locationId)));
                        } else if(criteria.getOperation() == CriteriaOperations.IS_NOT){
                            resultPattern.append(criteria.getRuleValues().stream().noneMatch(
                                    val -> val.equals(locationId)));
                        } else {
                            throw new IllegalArgumentException("Unexpected operation value" + criteria.getOperation());
                        }
                        break;
                    }
                    case "employee": {
                        log.info("employee field");
                        if(criteria.getOperation() == CriteriaOperations.IS){
                            resultPattern.append(criteria.getRuleValues().stream().anyMatch(val -> val.equals(initiatorUserId)));
                        } else if(criteria.getOperation() == CriteriaOperations.IS_NOT){
                            resultPattern.append(criteria.getRuleValues().stream().noneMatch(val -> val.equals(initiatorUserId)));
                        } else {
                            throw new IllegalArgumentException("Unexpected operation value" + criteria.getOperation());
                        }
                        break;
                    }
                    default: {
                        throw new IllegalArgumentException("Unexpected field value" + criteria.getField());
                    }
                }
                if (criteria.getConjunctiveCondition() != null && i != (workflowCriteriaList.size() - 1)) {
                    resultPattern.append(" ").append(criteria.getConjunctiveCondition()).append(" ");
                }

                log.info("this is the result pattern: "+ resultPattern);
            }
            log.info("this is the final result pattern: "+ resultPattern);
            if(resultPattern.isEmpty()){
                log.warn("cannot create pattern string for workflow: {}",workflowDefinition.getName());
            }

            Boolean isChosenWorkflow = expressionParser.parseExpression(resultPattern.toString()).getValue(Boolean.class);
            if(isChosenWorkflow == Boolean.TRUE){
                selectedWorkflowDefinition = workflowDefinition;
                break;
            }
        }

        RecordApproval recordApproval = RecordApproval.builder()
                .organization(organization)
                .organizationForms(formOptional.get())
                .recordId(recordId)
                .approvalStatus(ApprovalStatus.PENDING)
                .workflowInitiatorUser(initiatorUser)
                .approvalInitiatedDate(Instant.now())
                .build();


        List<ApproverTimeline> approverTimelines = new ArrayList<>();

        if (selectedWorkflowDefinition != null) {
            if (selectedWorkflowDefinition.getAutoApproval() != null) {
                ApprovalStatus status;
                if(selectedWorkflowDefinition.getAutoApproval() == AutoApproval.AUTO_APPROVE) {
                    status = ApprovalStatus.APPROVED;
                } else if(selectedWorkflowDefinition.getAutoApproval() == AutoApproval.AUTO_REJECT) {
                    status = ApprovalStatus.REJECTED;
                } else {
                    throw new IllegalArgumentException(
                            "Unexpected auto approval value: " + selectedWorkflowDefinition.getAutoApproval());
                }
                recordApproval.setApprovalStatus(status);
                recordApproval.setLastActionDate(Instant.now());
                recordApproval.setApprovalPendingWithUser(null);
                if (formOptional.get().getName().equals("Purchase Request")) {
                    PurchaseRequest purchaseRequest = purchaseRequestRepository
                            .findByIdAndOrganizationId(recordId,
                                    organizationId)
                            .orElseThrow(() -> new ResourceNotFoundException(
                                    "PurchaseRequest not found with id " + recordId));
                    if (purchaseRequest.getRequestStatus() != RequestStatus.PENDING) {
                        throw new RuntimeException("Purchase Request is not in pending status");
                    }
                    purchaseRequest.setRequestStatus(
                            status == ApprovalStatus.APPROVED ? RequestStatus.APPROVED : RequestStatus.REJECTED);
                    purchaseRequestRepository.save(purchaseRequest);
                }

                recordApprovalRepository.save(recordApproval);
                return toRecordApprovalResponse(recordApproval);
            } else if (!selectedWorkflowDefinition.getApprovals().isEmpty()) {
                for (Approval approval : selectedWorkflowDefinition.getApprovals()) {
                    switch (approval.getApproverType()) {
                        case DEPARTMENT_HEAD_RECORD_OWNER: {
                            if (initiatorUserDepartment == null) {
                                log.warn("initiator user does not have a department");
                                continue;
                            } else if (initiatorUserDepartment.getDepartmentHead() == null) {
                                log.warn("department head of record owner does not exist");
                                continue;
                            }

                            ApproverTimeline approverTimeline = ApproverTimeline.builder()
                                    .approvalStatus(ApprovalStatus.PENDING)
                                    .approver(initiatorUserDepartment.getDepartmentHead())
                                    .build();
                            approverTimelines.add(approverTimeline);
                            break;
                        }
                        case DEPARTMENT_HEAD: {
                            Long departmentHeadId = approval.getApproverId();

                            User departmentHead = userService.getById(departmentHeadId);
                            if (departmentHead == null) {
                                log.warn("Approval skipped: department head with ID {} does not exist",
                                        departmentHeadId);
                                continue;
                            }

                            Optional<Department> department = departmentRepository
                                    .findByDepartmentHeadIdAndOrganizationId(departmentHeadId, organizationId);

                            if (department.isEmpty()) {
                                log.warn("Approval skipped: department head '{}' (ID {}) has no department",
                                        departmentHead.getName(),
                                        departmentHeadId);
                                continue;
                            }

                            ApproverTimeline approverTimeline = ApproverTimeline.builder()
                                    .approvalStatus(ApprovalStatus.PENDING)
                                    .approver(departmentHead)
                                    .build();
                            approverTimelines.add(approverTimeline);
                            break;
                        }
                        case USER: {
                            User approverUser = userService.getById(approval.getApproverId());
                            if (approverUser == null) {
                                log.warn("The approving user does not exist {}", approval.getApproverId());
                                continue;
                            }

                            ApproverTimeline approverTimeline = ApproverTimeline.builder()
                                    .approvalStatus(ApprovalStatus.PENDING)
                                    .approver(approverUser)
                                    .build();
                            approverTimelines.add(approverTimeline);
                            break;
                        }
                        case REPORTING_TO: {
                            Long approvalLevels = approval.getApproverId();
                            User currentReportingManager = initiatorUser.getReportingManager();

                            for (int i = 0; i < approvalLevels; i++) {
                                if (currentReportingManager == null) {
                                    log.warn("user {} does not have a reporting manager at level: {}", initiatorUserId,
                                            i + 1);
                                    break;
                                }

                                ApproverTimeline approverTimeline = ApproverTimeline.builder()
                                        .approvalStatus(ApprovalStatus.PENDING)
                                        .approver(currentReportingManager)
                                        .build();
                                approverTimelines.add(approverTimeline);

                                currentReportingManager = currentReportingManager.getReportingManager();
                            }
                            break;
                        }

                        default:
                            throw new IllegalArgumentException("Unexpected value: " + approval.getApproverType());
                    }
                }
            } else {
                log.warn("approvals empty - cannot initiate workflow");
            }
        } else {
            log.warn("could not find any suitable workflow definition - cannot initiate workflow - adding org admin as the approver");

            ApproverTimeline approverTimeline = ApproverTimeline.builder()
                    .approvalStatus(ApprovalStatus.PENDING)
                    .approver(userRepository.findByOrganizationIdAndRole(organizationId, Role.ROLE_ORG_ADMIN)
                            .orElseThrow(() -> new AvinyaException("NO_ORG_ADMIN",
                                    "Organization admin does not exist for this organization")))
                    .build();
            approverTimelines.add(approverTimeline);

        }

        if(!approverTimelines.isEmpty()){
            recordApproval.setApprovalPendingWithUser(approverTimelines.get(0).getApprover());
            recordApproval.setLastActionDate(Instant.now());
            recordApprovalRepository.save(recordApproval);
            for(int i = 1;i<=approverTimelines.size();i++){
                approverTimelines.get(i-1).setRecordApproval(recordApproval);
                approverTimelines.get(i-1).setExecutionOrder(i);
            }
            approverTimelineRepository.saveAll(approverTimelines);
            return toRecordApprovalResponse(recordApproval);
        } else {
            log.warn("approver timelines is empty - cannot initate the workflow");
            return null;
        }
    }
    
    @Override
    @Transactional
    //TODO: get userid from security utils
    public RecordApprovalResponse approveRecord(Long organizationId, Long recordApprovalId, Long userId, String comment) {
        // Long userId = SecurityUtils.getCurrentUserId();
        Optional<RecordApproval> recordApprovalOptional = recordApprovalRepository.findByOrganizationIdAndId(organizationId, recordApprovalId);
        if (!recordApprovalOptional.isPresent()) {
            throw new RuntimeException("Record approval not found");
        } else if (recordApprovalOptional.get().getApprovalStatus() == ApprovalStatus.REJECTED) {
            throw new RuntimeException("Record approval is in rejected status");
        } else if (recordApprovalOptional.get().getApprovalStatus() != ApprovalStatus.PENDING) {
            throw new RuntimeException("Record approval is not in pending status");
        }
        
        RecordApproval recordApproval = recordApprovalOptional.get();
        
        List<ApproverTimeline> approverTimelines = approverTimelineRepository.findAllByRecordApprovalIdOrderByExecutionOrder(recordApprovalId);
        if (approverTimelines.isEmpty()) {
            throw new RuntimeException("No approver timelines found for this record approval");
        }

        boolean approverFound = false;
        for(ApproverTimeline timeline : approverTimelines) {
            if(timeline.getApprovalStatus() == ApprovalStatus.APPROVED) {
                continue;
            } else if(timeline.getApprover().getId().equals(userId) && recordApproval.getApprovalPendingWithUser().getId().equals(userId)) {
                timeline.setApprovalStatus(ApprovalStatus.APPROVED);
                timeline.setComment(comment);
                timeline.setStatusDate(Instant.now());
                approverTimelineRepository.save(timeline);
                approverFound = true;
                break;
            }
        }

        if (!approverFound) {
            throw new RuntimeException("User is not current approver for this record approval");
        }
        recordApproval.setLastActionUser(userService.getById(userId));

        Optional<ApproverTimeline> nextPendingApprover = approverTimelines.stream()
                .filter(timeline -> timeline.getApprovalStatus() == ApprovalStatus.PENDING)
                .findFirst();

        if (nextPendingApprover.isPresent()) {
            recordApproval.setApprovalPendingWithUser(nextPendingApprover.get().getApprover());
        } else {
            recordApproval.setApprovalStatus(ApprovalStatus.APPROVED);
            recordApproval.setApprovalPendingWithUser(null);
            if (recordApproval.getOrganizationForms().getName().equals("Purchase Request")) {
                PurchaseRequest purchaseRequest = purchaseRequestRepository
                        .findByIdAndOrganizationId(recordApproval.getRecordId(),
                                recordApproval.getOrganization().getId())
                        .orElseThrow(() -> new ResourceNotFoundException(
                                "PurchaseRequest not found with id " + recordApprovalId));
                if (purchaseRequest.getRequestStatus() != RequestStatus.PENDING) {
                    throw new RuntimeException("Purchase Request is not in pending status");
                }
                purchaseRequest.setRequestStatus(RequestStatus.APPROVED);
                purchaseRequestRepository.save(purchaseRequest);
                log.info("Purchase Request with ID {} has been approved", purchaseRequest.getId());
            }
        }
        recordApproval.setLastActionDate(Instant.now());

        recordApproval.setComment(comment);
        recordApproval = recordApprovalRepository.save(recordApproval);
        return toRecordApprovalResponse(recordApproval);
    }
    
    @Override
    @Transactional
    // TODO: get userid from security utils
    public RecordApprovalResponse rejectRecord(Long organizationId, Long recordApprovalId, Long userId, String comment) {
        // Long userId = SecurityUtils.getCurrentUserId();
        Optional<RecordApproval> recordApprovalOptional = recordApprovalRepository.findByOrganizationIdAndId(organizationId, recordApprovalId);
        if (!recordApprovalOptional.isPresent()) {
            throw new RuntimeException("Record approval not found");
        } else if (recordApprovalOptional.get().getApprovalStatus() != ApprovalStatus.PENDING) {
            throw new RuntimeException("Record approval is not in pending status");
        }
        
        RecordApproval recordApproval = recordApprovalOptional.get();

        List<ApproverTimeline> approverTimelines = approverTimelineRepository.findAllByRecordApprovalIdOrderByExecutionOrder(recordApprovalId);
        if (approverTimelines.isEmpty()) {
            throw new RuntimeException("No approver timelines found for this record approval");
        }

        boolean approverFound = false;
        for(ApproverTimeline timeline : approverTimelines) {
            if(timeline.getApprover().getId().equals(userId) && recordApproval.getApprovalPendingWithUser().getId().equals(userId)) {
                timeline.setApprovalStatus(ApprovalStatus.REJECTED);
                timeline.setComment(comment);
                timeline.setStatusDate(Instant.now());
                approverTimelineRepository.save(timeline);
                approverFound = true;
                break;
            }
        }

        if (!approverFound) {
            throw new RuntimeException("User is not current approver for this record approval");
        }
        recordApproval.setLastActionUser(userService.getById(userId));

        recordApproval.setApprovalStatus(ApprovalStatus.REJECTED);
        recordApproval.setComment(comment);
        recordApproval = recordApprovalRepository.save(recordApproval);

        if(recordApproval.getOrganizationForms().getName().equals("Purchase Request")) {
            Long recordId = recordApproval.getRecordId();
            PurchaseRequest purchaseRequest = purchaseRequestRepository.findByIdAndOrganizationId(recordId, recordApproval.getOrganization().getId())
                    .orElseThrow(() -> new ResourceNotFoundException("PurchaseRequest not found with id " + recordId));
            if(purchaseRequest.getRequestStatus() != RequestStatus.PENDING) {
                throw new RuntimeException("Purchase Request is not in pending status");
            }
            purchaseRequest.setRequestStatus(RequestStatus.REJECTED);
            purchaseRequestRepository.save(purchaseRequest);
            log.info("Purchase Request with ID {} has been rejected", purchaseRequest.getId());
        }

        return toRecordApprovalResponse(recordApproval);
    }
    
    @Override
    public List<RecordApprovalResponse> getPendingApprovals(Long organizationId, Long userId) {
        // Implementation would get all pending approvals for a user
        return recordApprovalRepository.findAllByOrganizationIdAndApprovalPendingWithUserIdAndApprovalStatus(
                organizationId, userId, ApprovalStatus.PENDING).stream()
                .map(this::toRecordApprovalResponse)
                .collect(Collectors.toList());
    }
    
    @Override
    public List<RecordApprovalResponse> getInitiatedApprovals(Long organizationId, Long userId) {
        // Implementation would get all approvals initiated by a user
        return recordApprovalRepository.findAllByOrganizationIdAndWorkflowInitiatorUserId(organizationId, userId).stream()
                .map(this::toRecordApprovalResponse)
                .collect(Collectors.toList());
    }
    
    @Override
    public List<ApproverTimelineResponse> getApprovalTimelineById(Long organizationId,Long recordApprovalId) {
        // Implementation would get the approval timeline for a record
        return approverTimelineRepository.findAllByRecordApprovalIdOrderByExecutionOrder(recordApprovalId).stream()
                .map(this::toApprovalTimelineResponse)
                .collect(Collectors.toList());
    }
    @Override
    public List<ApproverTimelineResponse> getApprovalTimelineByRecordId(Long organizationId, Long recordId) {
        RecordApproval recordApproval = recordApprovalRepository.findByOrganizationIdAndRecordId(organizationId, recordId).orElseThrow(() -> new ResourceNotFoundException("RecordApproval not found for organizationId: " + organizationId + " and recordId: " + recordId));
        // Implementation would get the approval timeline for a record
        return approverTimelineRepository.findAllByRecordApprovalIdOrderByExecutionOrder(recordApproval.getId()).stream()
                .map(this::toApprovalTimelineResponse)
                .collect(Collectors.toList());
    }

    @Override
    public Long getCurrentApproverUserId(Long organizationId, Long recordId) {
        RecordApproval recordApproval = recordApprovalRepository.findByOrganizationIdAndRecordId(organizationId, recordId)
                .orElseThrow(() -> new ResourceNotFoundException("RecordApproval not found for organizationId: " + organizationId + " and recordId: " + recordId));
        
        if (recordApproval.getApprovalPendingWithUser() == null) {
            return null; // No current approver
        }
        
        return recordApproval.getApprovalPendingWithUser().getId();
    }

    @Override
    public Long getLastActionUserId(Long organizationId, Long recordId) {
        RecordApproval recordApproval = recordApprovalRepository.findByOrganizationIdAndRecordId(organizationId, recordId)
                .orElseThrow(() -> new ResourceNotFoundException("RecordApproval not found for organizationId: " + organizationId + " and recordId: " + recordId));
        
        if (recordApproval.getLastActionUser() == null) {
            return null; // No last action user
        }
        
        return recordApproval.getLastActionUser().getId();
    }

    @Transactional
    private RecordApprovalResponse toRecordApprovalResponse(RecordApproval recordApproval) {
        if (recordApproval == null) {
            return null;
        }

        return RecordApprovalResponse.builder()
                .id(recordApproval.getId())
                .organizationId(
                        recordApproval.getOrganization() != null ? recordApproval.getOrganization().getId() : null)
                .formId(recordApproval.getOrganizationForms() != null ? recordApproval.getOrganizationForms().getId()
                        : null)
                .recordId(recordApproval.getRecordId())
                .initiatorUserId(recordApproval.getWorkflowInitiatorUser() != null
                        ? recordApproval.getWorkflowInitiatorUser().getId()
                        : null)
                .initiatorName(recordApproval.getWorkflowInitiatorUser() != null
                        ? recordApproval.getWorkflowInitiatorUser().getName()
                        : null)
                .status(recordApproval.getApprovalStatus())
                .formName(
                        recordApproval.getOrganizationForms() != null ? recordApproval.getOrganizationForms().getName()
                                : null)
                .createdAt(recordApproval.getCreatedDate() != null
                        ? LocalDateTime.ofInstant(recordApproval.getCreatedDate(), ZoneId.systemDefault())
                        : null)
                .updatedAt(recordApproval.getLastModifiedDate() != null
                        ? LocalDateTime.ofInstant(recordApproval.getLastModifiedDate(), ZoneId.systemDefault())
                        : null)
                .completedAt(recordApproval.getApprovalStatus() == ApprovalStatus.APPROVED ? (recordApproval.getLastActionDate() != null
                        ? LocalDateTime.ofInstant(recordApproval.getLastActionDate(), ZoneId.systemDefault())
                        : null) : null)
                .lastActionUserId(recordApproval.getLastActionUser() != null
                        ? recordApproval.getLastActionUser().getId()
                        : null)
                .lastActionUserName(recordApproval.getLastActionUser() != null
                        ? recordApproval.getLastActionUser().getName()
                        : null)
                .currentApproverName(recordApproval.getApprovalPendingWithUser() != null
                        ? recordApproval.getApprovalPendingWithUser().getName()
                        : null)
                .build();
    }

    @Transactional
    private ApproverTimelineResponse toApprovalTimelineResponse(ApproverTimeline timeline) {
        if (timeline == null) {
            return null;
        }

        return ApproverTimelineResponse.builder()
                .id(timeline.getId())
                .executionOrder(timeline.getExecutionOrder())
                .approverId(timeline.getApprover() != null ? timeline.getApprover().getId() : null)
                .approverName(timeline.getApprover() != null ? timeline.getApprover().getUsername() : null)
                .status(timeline.getApprovalStatus())
                .comment(timeline.getComment())
                .actionDate(timeline.getStatusDate() != null
                        ? LocalDateTime.ofInstant(timeline.getStatusDate(), ZoneId.systemDefault())
                        : null)
                .isCurrent(timeline.getRecordApproval().getApprovalPendingWithUser() == timeline.getApprover())
                .build();
    }
}