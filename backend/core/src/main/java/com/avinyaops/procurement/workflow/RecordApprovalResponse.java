package com.avinyaops.procurement.workflow;

import java.time.LocalDateTime;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RecordApprovalResponse {
    private Long id;
    private Long organizationId;
    private Long formId;
    private Long recordId;
    private Long initiatorUserId;
    private String initiatorName; // User's display name
    private Long lastActionUserId; // ID of the user who last acted on this approval
    private String lastActionUserName; // Name of the user who last acted on this approval
    private ApprovalStatus status;
    private String formName;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    private LocalDateTime completedAt;
    private String currentApproverName; // Name of the current approver (user or role)
}