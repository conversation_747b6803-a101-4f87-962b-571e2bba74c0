/**
 * Test script to verify form type dropdown prefilling in Edit Approval Workflow page
 * This script tests the fix for the form type dropdown not being prefilled correctly
 */

// Test data - simulating backend response
const mockWorkflowResponse = {
  id: 1,
  organizationId: 40928446087168,
  formId: 2, // This should be prefilled in the dropdown
  name: "Test Purchase Request Workflow",
  description: "Test workflow",
  isActive: true,
  criteria: [],
  approvers: [
    {
      id: 1,
      executionOrder: 1,
      approverType: "REPORTING_TO",
      approverId: 2, // 2 levels
      timeoutDays: null
    }
  ],
  autoApproval: null,
  createdAt: "2024-01-01T00:00:00Z",
  updatedAt: "2024-01-01T00:00:00Z"
};

const mockFormTypesResponse = [
  {
    id: 1,
    name: "Vendor Approval",
    description: "Vendor contract approvals",
    organizationId: 40928446087168,
    formSchema: "{}",
    active: true,
    createdAt: "2024-01-01T00:00:00Z",
    updatedAt: "2024-01-01T00:00:00Z"
  },
  {
    id: 2,
    name: "Purchase Request Approval",
    description: "Purchase request approvals",
    organizationId: 40928446087168,
    formSchema: "{}",
    active: true,
    createdAt: "2024-01-01T00:00:00Z",
    updatedAt: "2024-01-01T00:00:00Z"
  }
];

// Test the transformation logic
console.log("=== Testing Form Type Dropdown Prefilling Fix ===\n");

// Simulate the service transformation
function transformResponseToFrontend(response) {
  return {
    id: response.id.toString(),
    name: response.name,
    formId: response.formId, // This should be a number
    criteria: response.criteria || [],
    approvalLevels: response.approvers?.[0]?.approverId || 1,
    autoApprove: response.autoApproval === 'AUTO_APPROVE',
    autoReject: response.autoApproval === 'AUTO_REJECT',
    isActive: response.isActive,
    createdDate: response.createdAt,
    lastModifiedDate: response.updatedAt,
    workflowActionType: 'workflow-levels',
    approvals: response.approvers?.map(approver => ({
      approver: approver.approverId,
      approverType: approver.approverType
    })) || []
  };
}

function transformFormTypes(responses) {
  return responses.map(form => ({
    label: form.name,
    value: form.id, // This should be a number to match formId
    description: form.description || ''
  }));
}

// Test the transformations
const transformedWorkflow = transformResponseToFrontend(mockWorkflowResponse);
const transformedFormTypes = transformFormTypes(mockFormTypesResponse);

console.log("1. Backend workflow response:");
console.log("   formId:", mockWorkflowResponse.formId, "(type:", typeof mockWorkflowResponse.formId, ")");

console.log("\n2. Transformed workflow for frontend:");
console.log("   formId:", transformedWorkflow.formId, "(type:", typeof transformedWorkflow.formId, ")");

console.log("\n3. Form types dropdown options:");
transformedFormTypes.forEach((option, index) => {
  console.log(`   Option ${index + 1}: label="${option.label}", value=${option.value} (type: ${typeof option.value})`);
});

console.log("\n4. Prefilling test:");
const selectedFormType = transformedFormTypes.find(option => option.value === transformedWorkflow.formId);
if (selectedFormType) {
  console.log("   ✅ SUCCESS: Form type should be prefilled correctly");
  console.log(`   Selected option: "${selectedFormType.label}" (value: ${selectedFormType.value})`);
} else {
  console.log("   ❌ FAILURE: Form type will not be prefilled correctly");
  console.log(`   Looking for formId: ${transformedWorkflow.formId} (${typeof transformedWorkflow.formId})`);
  console.log("   Available values:", transformedFormTypes.map(opt => `${opt.value} (${typeof opt.value})`));
}

console.log("\n5. Form component state simulation:");
const formData = {
  name: transformedWorkflow.name,
  formId: transformedWorkflow.formId, // This should match dropdown option values
  criteria: transformedWorkflow.criteria,
  approvalLevels: transformedWorkflow.approvalLevels,
  autoApprove: transformedWorkflow.autoApprove,
  autoReject: transformedWorkflow.autoReject,
  actionType: transformedWorkflow.autoApprove ? 'auto-approve' : 
              transformedWorkflow.autoReject ? 'auto-reject' : 'approval'
};

console.log("   Form state formId:", formData.formId, "(type:", typeof formData.formId, ")");

// Test dropdown value matching
const dropdownValue = formData.formId;
const matchingOption = transformedFormTypes.find(opt => opt.value === dropdownValue);

if (matchingOption) {
  console.log("   ✅ Dropdown will show:", matchingOption.label);
} else {
  console.log("   ❌ Dropdown will show: (no selection - prefill failed)");
}

console.log("\n=== Test Complete ===");
console.log("\nKey fixes implemented:");
console.log("1. Service returns formId as number (not formType as string)");
console.log("2. FormTypeOption.value is number (not string)");
console.log("3. Form component uses formId consistently");
console.log("4. Dropdown value and formId types match for proper prefilling");
